name: Quality Checks

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  lint:
    name: Code Quality & Linting
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: Cache Gradle packages
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
            
      
    - name: Run Kotlin linting (ktlint)
      run: gradle ktlintCheck
      
    - name: Run compilation check
      run: gradle compileKotlin compileTestKotlin
      
    - name: Run tests
      run: gradle check
      
    - name: Generate test report
      uses: dorny/test-reporter@v2
      if: success() || failure()
      with:
        name: Test Results
        path: build/test-results/test/*.xml
        reporter: java-junit
        
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: build/test-results/
        
    - name: Upload build reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: build-reports
        path: build/reports/
