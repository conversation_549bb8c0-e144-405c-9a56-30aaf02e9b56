-- Add version column for optimistic locking to handle concurrency
-- This helps prevent lost updates when multiple instances modify the same rating

ALTER TABLE ratings ADD COLUMN version BIGINT NOT NULL DEFAULT 0;

-- Add comment to explain the purpose
COMMENT ON COLUMN ratings.version IS 'Version column for optimistic locking to handle concurrent updates';

-- Update existing records to have version 0
UPDATE ratings SET version = 0 WHERE version IS NULL;
