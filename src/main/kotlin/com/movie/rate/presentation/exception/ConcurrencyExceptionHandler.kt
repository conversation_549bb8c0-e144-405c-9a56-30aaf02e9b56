package com.movie.rate.presentation.exception

import com.movie.rate.domain.exceptions.ConcurrencyException
import com.movie.rate.domain.exceptions.DuplicateRatingException
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler

/**
 * Global exception handler for concurrency-related exceptions.
 * Handles exceptions that occur when multiple instances access the same data.
 */
@ControllerAdvice
class ConcurrencyExceptionHandler {

    @ExceptionHandler(ConcurrencyException::class)
    fun handleConcurrencyException(ex: ConcurrencyException): ResponseEntity<ErrorResponse> {
        val errorResponse = ErrorResponse(
            error = "Concurrency Conflict",
            message = ex.message ?: "The resource was modified by another process. Please retry your request.",
            status = HttpStatus.CONFLICT.value(),
        )
        return ResponseEntity.status(HttpStatus.CONFLICT).body(errorResponse)
    }

    @ExceptionHandler(DuplicateRatingException::class)
    fun handleDuplicateRatingException(ex: DuplicateRatingException): ResponseEntity<ErrorResponse> {
        val errorResponse = ErrorResponse(
            error = "Duplicate Rating",
            message = ex.message ?: "A rating already exists for this user and movie combination.",
            status = HttpStatus.CONFLICT.value(),
        )
        return ResponseEntity.status(HttpStatus.CONFLICT).body(errorResponse)
    }

    data class ErrorResponse(
        val error: String,
        val message: String,
        val status: Int,
        val timestamp: Long = System.currentTimeMillis(),
    )
}
