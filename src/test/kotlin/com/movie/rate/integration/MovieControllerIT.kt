package com.movie.rate.integration

import com.movie.rate.integration.Constants.Companion.MOVIES_ENDPOINT
import io.restassured.RestAssured.given
import io.restassured.http.ContentType
import org.hamcrest.Matchers.containsString
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.greaterThanOrEqualTo
import org.hamcrest.Matchers.hasSize
import org.hamcrest.Matchers.notNullValue

import org.junit.jupiter.api.Test
import org.springframework.boot.actuate.endpoint.web.WebEndpointResponse.STATUS_BAD_REQUEST
import org.springframework.boot.actuate.endpoint.web.WebEndpointResponse.STATUS_NOT_FOUND
import org.springframework.boot.actuate.endpoint.web.WebEndpointResponse.STATUS_OK
import org.springframework.http.HttpStatus
import org.springframework.http.HttpStatusCode
import org.springframework.test.annotation.DirtiesContext

