package com.movie.rate.integration

import com.movie.rate.infrastructure.entities.MovieJpaEntity
import com.movie.rate.infrastructure.entities.RatingJpaEntity
import com.movie.rate.infrastructure.entities.UserJpaEntity
import com.movie.rate.infrastructure.persistence.repositories.MovieJpaRepository
import com.movie.rate.infrastructure.persistence.repositories.RatingJpaRepository
import com.movie.rate.infrastructure.persistence.repositories.UserJpaRepository
import com.movie.rate.integration.Constants.Companion.RATINGS_ENDPOINT
import com.movie.rate.integration.Constants.Companion.USERS_ENDPOINT
import io.restassured.RestAssured.given
import io.restassured.http.ContentType
import org.hamcrest.Matchers.containsString
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.hamcrest.Matchers.notNullValue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.test.annotation.DirtiesContext
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*
import kotlin.random.Random

/**
 * End-to-end integration tests for Movie Rating System workflows.
 * These tests verify complete business scenarios with isolated data:
 * - Complete rating workflows (User → Movie → Rating)
 * - Business logic scenarios (duplicate ratings, user rating history)
 * - Cross-domain interactions and data consistency
 *
 * Each test creates its own isolated data set to avoid interference.
 * Basic CRUD operations are tested in individual controller integration tests.
 */
@DirtiesContext
class MovieRatingSystemIT : BaseIntegrationTest() {

    @Autowired
    lateinit var ratingRepository: RatingJpaRepository

    @Autowired
    lateinit var movieRepository: MovieJpaRepository

    @Autowired
    lateinit var userRepository: UserJpaRepository

    @BeforeEach
    override fun setUp() {
        super.setUp()
        // Clean up any existing data
        ratingRepository.deleteAll()
        movieRepository.deleteAll()
        userRepository.deleteAll()
    }

    @Test
    fun `should create rating with valid data`() {
        // Create a user using programmatic approach
        val userUuid = UUID.randomUUID()
        val user = UserJpaEntity(
            uuid = userUuid,
            email = "<EMAIL>",
            username = "ratingtest",
            fullName = "Test User",
            createdAt = LocalDateTime.now(),
            updatedAt = null,
        )

        userRepository.save(user)

        // Create a movie using programmatic approach
        val movieUuid = UUID.randomUUID()
        val movie = MovieJpaEntity(
            uuid = movieUuid,
            title = "Rating Test Movie",
            description = "A test movie for rating testing",
            releaseDate = LocalDate.of(2024, 1, 1),
            genre = "Action",
            director = "Test Director",
            createdAt = LocalDateTime.now(),
            updatedAt = null,
        )

        movieRepository.save(movie)
        // Create rating using programmatic approach
        val ratingRequest = createTestRatingRequest(
            userId = "$userUuid",
            movieId = "$movieUuid",
                value = 5,
                comment = "Absolutely fantastic movie! Great storyline and excellent acting.",
            )

        given()
            .contentType(ContentType.JSON)
            .body(ratingRequest)
            .`when`()
            .post(RATINGS_ENDPOINT)
            .then()
            .statusCode(HttpStatus.CREATED.value())
            .body("user_id", equalTo("$userUuid"))
            .body("movie_id", equalTo("$movieUuid"))
            .body("value", equalTo(5))
            .body("comment", containsString("Absolutely fantastic movie"))
            .body("created_at", notNullValue())
    }

    @Test
    fun `should retrieve user ratings successfully`() {
        // Create a user using programmatic approach
        val userUuid = UUID.randomUUID()
        val user = UserJpaEntity(
            uuid = userUuid,
            email = "<EMAIL>",
            username = "ratingtest",
            fullName = "Test User",
            createdAt = LocalDateTime.now(),
            updatedAt = null,
        )

        userRepository.save(user)

        // Create a movie using programmatic approach
        val movieUuid = UUID.randomUUID()
        val movie = MovieJpaEntity(
            uuid = movieUuid,
            title = "Rating Test Movie",
            description = "A test movie for rating testing",
            releaseDate = LocalDate.of(2024, 1, 1),
            genre = "Action",
            director = "Test Director",
            createdAt = LocalDateTime.now(),
            updatedAt = null,
        )

        movieRepository.save(movie)

        // Create rating
        val rating = RatingJpaEntity(
            userUuid = userUuid,
            movieUuid = UUID.fromString("$movieUuid"),
            value = 5,
            comment = "Absolutely fantastic movie! Great storyline and excellent acting.",
            createdAt = LocalDateTime.now(),
            updatedAt = null,
        )
        ratingRepository.save(rating)

        // Test retrieving user ratings
        given()
            .`when`()
            .get("/api/ratings/user/$userUuid")
            .then()
            .statusCode(200)
            .body("$", hasSize<Any>(1))
            .body("[0].user_id", equalTo("$userUuid"))
            .body("[0].movie_id", equalTo("$movieUuid"))
            .body("[0].value", equalTo(5))
    }

    @Test
    fun `should update existing rating when creating duplicate`() {
        // Create a user using programmatic approach
        val userUuid = UUID.randomUUID()
        val user = UserJpaEntity(
            uuid = userUuid,
            email = "<EMAIL>",
            username = "duplicatetest",
            fullName = "Duplicate Test User",
            createdAt = LocalDateTime.now(),
            updatedAt = null,
        )

        userRepository.save(user)

        // Create a movie using programmatic approach
        val movieUuid = UUID.randomUUID()
        val movie = MovieJpaEntity(
            uuid = movieUuid,
            title = "Duplicate Rating Test Movie",
            description = "A test movie for duplicate rating testing",
            releaseDate = LocalDate.of(2024, 1, 1),
            genre = "Action",
            director = "Test Director",
            createdAt = LocalDateTime.now(),
            updatedAt = null,
        )

        movieRepository.save(movie)

        // Create initial rating using programmatic approach
        val initialRating = RatingJpaEntity(
            userUuid = userUuid,
            movieUuid = movieUuid,
            value = 5,
            comment = "Initial rating comment",
            createdAt = LocalDateTime.now(),
            updatedAt = null,
        )
        ratingRepository.save(initialRating)

        // Verify initial rating exists
        given()
            .`when`()
            .get("/api/ratings/user/${userUuid}")
            .then()
            .statusCode(200)
            .body("$", hasSize<Any>(1))
            .body("[0].value", equalTo(5))
            .body("[0].comment", equalTo("Initial rating comment"))

        // Create duplicate rating (should update existing) using programmatic approach
        val duplicateRatingRequest = createTestRatingRequest(
            userId = "$userUuid",
            movieId = "$movieUuid",
                value = 3,
                comment = "Updated rating comment",
            )

        // Try to create duplicate rating - this should update the existing one
        val duplicateResponse = given()
                .contentType(ContentType.JSON)
                .body(duplicateRatingRequest)
                .`when`()
                .post(RATINGS_ENDPOINT)
                .then()
                .extract()
                .response()

        // Verify the duplicate rating was handled successfully
        assert(duplicateResponse.statusCode == HttpStatus.CREATED.value()) {
            "Expected status 201, but got ${duplicateResponse.statusCode}. Response: ${duplicateResponse.asString()}"
        }

        // Verify the response contains the updated values
        assert(duplicateResponse.path<String>("user_id") == "$userUuid") {
            "Expected user_id to be $userUuid, but got ${duplicateResponse.path<String>("user_id")}"
        }
        assert(duplicateResponse.path<String>("movie_id") == "$movieUuid") {
            "Expected movie_id to be $movieUuid, but got ${duplicateResponse.path<String>("movie_id")}"
        }
        assert(duplicateResponse.path<Int>("value") == 3)
        assert(duplicateResponse.path<String>("comment") == "Updated rating comment")

        // Verify that there's still only one rating for this user-movie combination
        // and it has the updated values
        given()
            .`when`()
            .get("$RATINGS_ENDPOINT/user/$userUuid")
            .then()
            .statusCode(200)
            .body("$", hasSize<Any>(1)) // Should still be only 1 rating
            .body("[0].value", equalTo(3)) // Should have the updated value
            .body("[0].comment", equalTo("Updated rating comment")) // Should have the updated comment
            .body("[0].created_at", notNullValue()) // Should have created timestamp
            .body("[0].updated_at", notNullValue()) // Should have updated timestamp
    }

    @Test
    fun `should display user profile with all rated movies`() {
        // Create a user for the profile test
        val userUuid = UUID.randomUUID()
        UserJpaEntity(
            uuid = userUuid,
            email = "<EMAIL>",
            username = "profiletest",
            fullName = "Profile Test User",
            createdAt = LocalDateTime.now(),
            updatedAt = null,
        ).let { userRepository.save(it) }

        // Create multiple movies for the user profile
        // create a list of movies using stream and save it into a val
        val movies = (1..3).map {
            MovieJpaEntity(
                uuid = UUID.randomUUID(),
                title = "Profile Test Movie $it",
                description = "A test movie for profile testing",
                releaseDate = LocalDate.of(2024, 1, 1),
                genre = "Action",
                director = "Test Director",
                createdAt = LocalDateTime.now(),
                updatedAt = null,
            ).let { movieRepository.save(it) }
        }

        // Rate both movies with different ratings (valid range: 1-5)

        for (movie in movies) {
            RatingJpaEntity(
                userUuid = userUuid,
                movieUuid = movie.uuid,
                value = Random.nextInt(1, 6), // Random rating between 1 and 5
                comment = "Rating for movie ${movie.title}",
                createdAt = LocalDateTime.now(),
                updatedAt = null,
            ).let { ratingRepository.save(it) }
        }
        // Test retrieving the user ratings (not user profile - that would be /users/{id})
        given()
            .`when`()
            .get("$RATINGS_ENDPOINT/user/$userUuid")
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("size()", equalTo(3)) // Should have 3 ratings
            .body("[0].user_id", equalTo(userUuid.toString())) // Check first rating
            .body("[1].user_id", equalTo(userUuid.toString())) // Check second rating
            .body("[2].user_id", equalTo(userUuid.toString())) // Check third rating
            .body("[0].value", notNullValue())
            .body("[0].comment", notNullValue())
    }

    private fun createTestRatingRequest(
        userId: String,
        movieId: String,
        value: Int,
        comment: String,
    ): Map<String, Any> =
        mapOf(
            "user_id" to userId,
            "movie_id" to movieId,
            "value" to value,
            "comment" to comment,
        )
}
