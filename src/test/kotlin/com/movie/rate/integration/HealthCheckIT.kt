package com.movie.rate.integration

import io.restassured.RestAssured.given
import org.hamcrest.Matchers.equalTo

import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

/**
 * Integration tests for system health monitoring.
 * Tests application health and monitoring endpoints.
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
class HealthCheckIT : BaseIntegrationTest() {

    @Test
    fun `should provide health check endpoint with UP status`() {
        given()
            .`when`()
            .get("/actuator/health")
            .then()
            .statusCode(200)
            .body("status", equalTo("UP"))
    }
}
