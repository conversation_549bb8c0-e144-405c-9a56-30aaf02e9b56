package com.movie.rate.application.usecases

import com.movie.rate.application.dto.CreateRatingRequest
import com.movie.rate.domain.entities.Movie
import com.movie.rate.domain.entities.Rating
import com.movie.rate.domain.entities.User
import com.movie.rate.domain.repositories.MovieRepository
import com.movie.rate.domain.repositories.RatingRepository
import com.movie.rate.domain.repositories.UserRepository
import com.movie.rate.domain.services.IdempotencyService
import com.movie.rate.domain.valueobjects.Email
import com.movie.rate.domain.valueobjects.IdempotencyKey
import com.movie.rate.domain.valueobjects.MovieId
import com.movie.rate.domain.valueobjects.RatingValue
import com.movie.rate.domain.valueobjects.UserId
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime

class IdempotentCreateRatingUseCaseTest {

    private lateinit var ratingRepository: RatingRepository
    private lateinit var userRepository: UserRepository
    private lateinit var movieRepository: MovieRepository
    private lateinit var idempotencyService: IdempotencyService
    private lateinit var useCase: IdempotentCreateRatingUseCase

    private lateinit var testUser: User
    private lateinit var testMovie: Movie

    @BeforeEach
    fun setUp() {
        ratingRepository = mockk()
        userRepository = mockk()
        movieRepository = mockk()
        idempotencyService = mockk()
        
        useCase = IdempotentCreateRatingUseCase(
            ratingRepository,
            userRepository,
            movieRepository,
            idempotencyService
        )

        testUser = User.create(
            id = UserId.generate(),
            email = Email.of("<EMAIL>"),
            username = "testuser",
            fullName = "Test User"
        )

        testMovie = Movie.create(
            id = MovieId.generate(),
            title = "Test Movie",
            description = "A test movie",
            releaseDate = LocalDate.of(2023, 1, 1),
            genre = "Test",
            director = "Test Director"
        )
    }

    @Test
    fun `should return cached result when idempotency key already exists`() {
        // Given
        val request = CreateRatingRequest(
            userId = testUser.id.value.toString(),
            movieId = testMovie.id.value.toString(),
            value = 5,
            comment = "Great movie!"
        )
        val idempotencyKey = IdempotencyKey.fromString("test-key")
        val cachedResponse = mockk<com.movie.rate.application.dto.RatingResponse>()

        every { idempotencyService.getResult<com.movie.rate.application.dto.RatingResponse>(idempotencyKey) } returns cachedResponse

        // When
        val result = useCase.execute(request, idempotencyKey)

        // Then
        assertEquals(cachedResponse, result)
        verify(exactly = 0) { userRepository.findById(any()) }
        verify(exactly = 0) { movieRepository.findById(any()) }
        verify(exactly = 0) { ratingRepository.findByUserIdAndMovieId(any(), any()) }
    }

    @Test
    fun `should create new rating and cache result when no existing rating`() {
        // Given
        val request = CreateRatingRequest(
            userId = testUser.id.value.toString(),
            movieId = testMovie.id.value.toString(),
            value = 5,
            comment = "Great movie!"
        )
        val idempotencyKey = IdempotencyKey.fromString("test-key")
        val savedRating = Rating.create(
            userId = testUser.id,
            movieId = testMovie.id,
            value = RatingValue.of(5),
            comment = "Great movie!"
        )

        every { idempotencyService.getResult<com.movie.rate.application.dto.RatingResponse>(idempotencyKey) } returns null
        every { idempotencyService.tryLock(idempotencyKey) } returns true
        every { idempotencyService.releaseLock(idempotencyKey) } returns Unit
        every { userRepository.findById(testUser.id) } returns testUser
        every { movieRepository.findById(testMovie.id) } returns testMovie
        every { ratingRepository.findByUserIdAndMovieId(testUser.id, testMovie.id) } returns null
        every { ratingRepository.save(any<Rating>()) } returns savedRating

        val resultSlot = slot<com.movie.rate.application.dto.RatingResponse>()
        every { idempotencyService.storeResult(idempotencyKey, capture(resultSlot)) } returns Unit

        // When
        val result = useCase.execute(request, idempotencyKey)

        // Then
        assertNotNull(result)
        assertEquals(savedRating.value.value, result.value)
        assertEquals(savedRating.comment, result.comment)
        
        verify { idempotencyService.storeResult(idempotencyKey, any()) }
        verify { idempotencyService.releaseLock(idempotencyKey) }
    }

    @Test
    fun `should update existing rating and cache result`() {
        // Given
        val request = CreateRatingRequest(
            userId = testUser.id.value.toString(),
            movieId = testMovie.id.value.toString(),
            value = 4,
            comment = "Updated comment"
        )
        val idempotencyKey = IdempotencyKey.fromString("test-key")
        val existingRating = Rating.create(
            userId = testUser.id,
            movieId = testMovie.id,
            value = RatingValue.of(5),
            comment = "Original comment"
        )

        every { idempotencyService.getResult<com.movie.rate.application.dto.RatingResponse>(idempotencyKey) } returns null
        every { idempotencyService.tryLock(idempotencyKey) } returns true
        every { idempotencyService.releaseLock(idempotencyKey) } returns Unit
        every { userRepository.findById(testUser.id) } returns testUser
        every { movieRepository.findById(testMovie.id) } returns testMovie
        every { ratingRepository.findByUserIdAndMovieId(testUser.id, testMovie.id) } returns existingRating
        every { ratingRepository.save(existingRating) } returns existingRating

        val resultSlot = slot<com.movie.rate.application.dto.RatingResponse>()
        every { idempotencyService.storeResult(idempotencyKey, capture(resultSlot)) } returns Unit

        // When
        val result = useCase.execute(request, idempotencyKey)

        // Then
        assertNotNull(result)
        assertEquals(4, result.value)
        assertEquals("Updated comment", result.comment)
        
        verify { idempotencyService.storeResult(idempotencyKey, any()) }
        verify { idempotencyService.releaseLock(idempotencyKey) }
    }

    @Test
    fun `should generate deterministic idempotency key when none provided`() {
        // Given
        val request = CreateRatingRequest(
            userId = testUser.id.value.toString(),
            movieId = testMovie.id.value.toString(),
            value = 5,
            comment = "Great movie!"
        )
        val expectedKey = IdempotencyKey.forRating(request.userId, request.movieId)

        every { idempotencyService.getResult<com.movie.rate.application.dto.RatingResponse>(expectedKey) } returns null
        every { idempotencyService.tryLock(expectedKey) } returns true
        every { idempotencyService.releaseLock(expectedKey) } returns Unit
        every { userRepository.findById(testUser.id) } returns testUser
        every { movieRepository.findById(testMovie.id) } returns testMovie
        every { ratingRepository.findByUserIdAndMovieId(testUser.id, testMovie.id) } returns null
        every { ratingRepository.save(any<Rating>()) } returns mockk()
        every { idempotencyService.storeResult(expectedKey, any()) } returns Unit

        // When
        useCase.execute(request, null)

        // Then
        verify { idempotencyService.getResult<com.movie.rate.application.dto.RatingResponse>(expectedKey) }
        verify { idempotencyService.tryLock(expectedKey) }
        verify { idempotencyService.storeResult(expectedKey, any()) }
        verify { idempotencyService.releaseLock(expectedKey) }
    }
}
