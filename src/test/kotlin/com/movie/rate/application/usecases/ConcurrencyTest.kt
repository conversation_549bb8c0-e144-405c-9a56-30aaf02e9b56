package com.movie.rate.application.usecases

import com.movie.rate.application.dto.CreateRatingRequest
import com.movie.rate.domain.exceptions.DuplicateRatingException
import com.movie.rate.infrastructure.entities.MovieJpaEntity
import com.movie.rate.infrastructure.entities.UserJpaEntity
import com.movie.rate.infrastructure.persistence.repositories.MovieJpaRepository
import com.movie.rate.infrastructure.persistence.repositories.RatingJpaRepository
import com.movie.rate.infrastructure.persistence.repositories.UserJpaRepository
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import java.util.concurrent.CompletableFuture
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.atomic.AtomicInteger


/**
 * Test class to verify idempotent behavior in rating creation.
 * Tests that duplicate rating requests produce consistent results.
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class ConcurrencyTest {

    @Autowired
    private lateinit var createRatingUseCase: CreateRatingUseCase

    @Autowired
    private lateinit var userJpaRepository: UserJpaRepository

    @Autowired
    private lateinit var movieJpaRepository: MovieJpaRepository

    @Autowired
    private lateinit var ratingJpaRepository: RatingJpaRepository

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    private lateinit var testUserId: String
    private lateinit var testMovieId: String

    @BeforeEach
    fun setUp() {
        // Use separate transaction to commit test data so concurrent threads can see it
        transactionTemplate.execute {
            // Clean up any existing data
            ratingJpaRepository.deleteAll()
            userJpaRepository.deleteAll()
            movieJpaRepository.deleteAll()

            // Create test user
            val userUuid = UUID.randomUUID()
            val user = UserJpaEntity(
                uuid = userUuid,
                email = "<EMAIL>",
                username = "testuser",
                fullName = "Test User",
                createdAt = LocalDateTime.now(),
                updatedAt = null,
            )
            userJpaRepository.save(user)
            testUserId = userUuid.toString()

            // Create test movie
            val movieUuid = UUID.randomUUID()
            val movie = MovieJpaEntity(
                uuid = movieUuid,
                title = "Test Movie",
                description = "A test movie",
                releaseDate = LocalDate.of(2023, 1, 1),
                genre = "Test",
                director = "Test Director",
                createdAt = LocalDateTime.now(),
                updatedAt = null,
            )
            movieJpaRepository.save(movie)
            testMovieId = movieUuid.toString()
        }
    }

    @AfterEach
    fun tearDown() {
        // Clean up after each test in separate transaction
        transactionTemplate.execute {
            ratingJpaRepository.deleteAll()
            userJpaRepository.deleteAll()
            movieJpaRepository.deleteAll()
        }
    }

    @Test
    fun `should handle duplicate rating requests idempotently`() {
        val request = CreateRatingRequest(
            userId = testUserId,
            movieId = testMovieId,
            value = 5,
            comment = "Great movie!",
        )

        // Create first rating
        val firstResponse = createRatingUseCase.execute(request)

        // Create "duplicate" rating with same values - should be idempotent
        val secondResponse = createRatingUseCase.execute(request)

        // Both responses should be identical (idempotent behavior)
        assertEquals(firstResponse.value, secondResponse.value)
        assertEquals(firstResponse.comment, secondResponse.comment)
        assertEquals(firstResponse.userId, secondResponse.userId)
        assertEquals(firstResponse.movieId, secondResponse.movieId)

        // Verify only one rating exists in database
        val ratings = ratingJpaRepository.findByUserUuid(UUID.fromString(testUserId))
        assertEquals(1, ratings.size, "Only one rating should exist despite multiple requests")
    }

    @Test
    fun `should handle concurrent rating creation attempts gracefully`() {
        val request = CreateRatingRequest(
            userId = testUserId,
            movieId = testMovieId,
            value = 5,
            comment = "Great movie!",
        )

        val executor: ExecutorService = Executors.newFixedThreadPool(10)
        val successCount = AtomicInteger(0)
        val exceptionCount = AtomicInteger(0)
        val futures = mutableListOf<CompletableFuture<Void>>()

        // Simulate 10 concurrent attempts to create the same rating
        repeat(10) { threadNum ->
            val future = CompletableFuture.runAsync({
                try {
                    val response = createRatingUseCase.execute(request)
                    successCount.incrementAndGet()
                    println("Thread $threadNum succeeded: ${response.value}")
                } catch (ex: Exception) {
                    exceptionCount.incrementAndGet()
                    println("Thread $threadNum failed: ${ex.javaClass.simpleName}: ${ex.message}")
                }
            }, executor)
            futures.add(future)
        }

        // Wait for all attempts to complete
        CompletableFuture.allOf(*futures.toTypedArray()).join()

        println("Success count: ${successCount.get()}, Exception count: ${exceptionCount.get()}")

        // All requests should succeed due to idempotent behavior
        // (either create new or update existing)
        assertEquals(10, successCount.get(), "All requests should succeed due to idempotent behavior")

        // Verify only one rating exists in database (due to unique constraint)
        val ratings = ratingJpaRepository.findByUserUuid(UUID.fromString(testUserId))
        assertEquals(1, ratings.size, "Only one rating should exist despite concurrent attempts")

        // Verify the final rating has the expected values
        assertEquals(5, ratings[0].value)
        assertEquals("Great movie!", ratings[0].comment)

        executor.shutdown()
    }
}
