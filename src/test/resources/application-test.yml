spring:
  application:
    name: movie-rating-system-test

  datasource:
    url: jdbc:tc:postgresql:15.1:///integration-tests-db
    driver-class-name: org.testcontainers.jdbc.ContainerDatabaseDriver
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        dialect: org.hibernate.dialect.PostgreSQLDialect

# Test logging configuration - minimal logging for faster tests
logging:
  level:
    root: WARN
    com.movie.rate: DEBUG
