[*]
insert_final_newline = true
charset = utf-8
end_of_line = lf
indent_style = space
indent_size = 4
trim_trailing_whitespace = true

[*.{kt,kts}]
# Kotlin specific settings
indent_size = 4
continuation_indent_size = 4

# ktlint rules configuration - disable strict formatting rules for more flexibility
ktlint_chain-wrapping = disabled
ktlint_multiline-expression-wrapping = disabled
ktlint_standard_multiline-expression-wrapping = disabled
ktlint_standard_chain-wrapping = disabled
ktlint_standard_trailing-comma-on-call-site = disabled
ktlint_standard_trailing-comma-on-declaration-site = disabled
